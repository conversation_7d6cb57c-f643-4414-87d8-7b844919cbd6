#include "csvfile.h"
#include "smoke_analyzer_comm.h"
#include <errno.h>
#include <time.h>
#include <stdio.h>
#ifdef _WIN32
    #include <direct.h>
    #include <windows.h>
    #define mkdir(path, mode) _mkdir(path)
#else
    #include <sys/stat.h>
    #include <sys/types.h>
    #include <unistd.h>
#endif

// 获取可执行文件所在目录
static void get_executable_dir(char *dir_path, size_t size) {
#ifdef _WIN32
    char exe_path[MAX_PATH];
    DWORD len = GetModuleFileNameA(NULL, exe_path, MAX_PATH);
    if (len > 0) {
        // 找到最后一个反斜杠的位置
        char *last_slash = strrchr(exe_path, '\\');
        if (last_slash) {
            *last_slash = '\0';  // 截断到目录部分
            strncpy(dir_path, exe_path, size - 1);
            dir_path[size - 1] = '\0';
        } else {
            strcpy(dir_path, ".");
        }
    } else {
        strcpy(dir_path, ".");
    }
#else
    char exe_path[1024];
    ssize_t len = readlink("/proc/self/exe", exe_path, sizeof(exe_path) - 1);
    if (len != -1) {
        exe_path[len] = '\0';
        char *last_slash = strrchr(exe_path, '/');
        if (last_slash) {
            *last_slash = '\0';
            strncpy(dir_path, exe_path, size - 1);
            dir_path[size - 1] = '\0';
        } else {
            strcpy(dir_path, ".");
        }
    } else {
        strcpy(dir_path, ".");
    }
#endif
}

// 初始化CSV管理器
int init_csv_manager(csv_manager_t *csv, size_t max_size, size_t flush_threshold, int flush_interval, const char *fileprefix) {
    memset(csv, 0, sizeof(csv_manager_t));
    csv->max_size = max_size;
    csv->flush_threshold = flush_threshold;
    csv->flush_interval = flush_interval;
    csv->last_flush_time = time(NULL);
    strcpy(csv->fileprefix,fileprefix);
    csv->current_date = 0;  // 初始化为0，表示还没有设置日期
    csv->creating_file = 0; // 初始化文件创建状态标志



    // 获取可执行文件所在目录
    char exe_dir[512];
    get_executable_dir(exe_dir, sizeof(exe_dir));

    // 构建data目录的完整路径
    char data_dir[600];
    snprintf(data_dir, sizeof(data_dir), "%s%sdata", exe_dir,
#ifdef _WIN32
        "\\"
#else
        "/"
#endif
    );

    // 创建数据目录（如果不存在）
    debug_printf("尝试创建数据目录: %s\n", data_dir);
    int result = mkdir(data_dir, 0755);
    if (result == 0) {
        debug_printf("数据目录创建成功: %s\n", data_dir);
    } else if (errno == EEXIST) {
        debug_printf("数据目录已存在: %s\n", data_dir);
    } else {
        debug_printf("数据目录创建失败: %s, 错误: %s (errno=%d)\n", data_dir, strerror(errno), errno);

        // 尝试使用当前目录下的data文件夹作为备选方案
        debug_printf("尝试在当前目录创建data文件夹作为备选方案\n");
        result = mkdir("data", 0755);
        if (result == 0) {
            debug_printf("当前目录下data文件夹创建成功\n");
        } else if (errno == EEXIST) {
            debug_printf("当前目录下data文件夹已存在\n");
        } else {
            debug_printf("当前目录下创建data文件夹也失败: %s (errno=%d)\n", strerror(errno), errno);
            debug_printf("警告: 无法创建数据目录，CSV文件可能无法正常创建\n");
        }
    }

    return 0;
}

// 创建新的CSV文件
int create_new_csv_file(csv_manager_t *csv) {
    // 防止重复创建文件
    if (csv->creating_file) {
        debug_printf("文件正在创建中，跳过重复创建请求\n");
        return 0;
    }

    // 设置创建状态标志
    csv->creating_file = 1;

    // 关闭已打开的文件
    if(csv->file) {
        fflush(csv->file);
        fclose(csv->file);
        csv->file = NULL;
    }

    // 获取当前时间戳
    time_t now = time(NULL);
    csv->start_time = now;

    // 格式化文件名（时间戳秒数）
    struct tm *timeinfo = localtime(&now);
    char today[60] = {0};
    strftime(today, sizeof(today), "%Y%m%d.csv", timeinfo);

    // 更新当前日期
    csv->current_date = (timeinfo->tm_year + 1900) * 10000 +
                       (timeinfo->tm_mon + 1) * 100 +
                       timeinfo->tm_mday;

    // 获取可执行文件所在目录
    char exe_dir[512];
    get_executable_dir(exe_dir, sizeof(exe_dir));

    // 使用固定的 boiler 前缀和日期格式，确保每天创建新文件
    char safe_prefix[128];
    snprintf(safe_prefix, sizeof(safe_prefix), "boiler_%04d%02d%02d",
             timeinfo->tm_year + 1900,
             timeinfo->tm_mon + 1,
             timeinfo->tm_mday);
    snprintf(csv->filename, sizeof(csv->filename), "%s%sdata%s%s.csv", exe_dir,
#ifdef _WIN32
        "\\", "\\"
#else
        "/", "/"
#endif
        , safe_prefix);

    // 检查文件是否存在
    int file_exists = (access(csv->filename, F_OK) == 0);

    //如果启用文件最大小机制，则需要更换文件名
    if (csv->max_size > 0 && file_exists) {
        // 检查文件大小是否超过限制
        FILE *temp_file = fopen(csv->filename, "r");
        if (temp_file) {
            fseek(temp_file, 0, SEEK_END);
            long file_size = ftell(temp_file);
            fclose(temp_file);

            if (file_size >= csv->max_size) {
                // 使用日期+序号的方式，而不是时间戳
                static int file_counter = 1;
                snprintf(safe_prefix, sizeof(safe_prefix), "boiler_%04d%02d%02d",
                         timeinfo->tm_year + 1900,
                         timeinfo->tm_mon + 1,
                         timeinfo->tm_mday);
                snprintf(csv->filename, sizeof(csv->filename), "%s%sdata%s%s_%d.csv", exe_dir,
#ifdef _WIN32
                    "\\", "\\"
#else
                    "/", "/"
#endif
                    , safe_prefix, file_counter++);
                file_exists = 0; // 新文件不存在，需要写表头
            }
        }
    }

    // 以追加模式打开文件，确保不会覆盖现有内容
    csv->file = fopen(csv->filename, "a");
    if(!csv->file) {
        debug_perror("创建CSV文件失败");
        return -1;
    }

    // 写入UTF-8 BOM标记
    // unsigned char bom[3] = {0xEF, 0xBB, 0xBF};
    // fwrite(bom, 1, 3, csv->file);

    // 只有当文件不存在时才写入表头（带单位）
    if (!file_exists) {
        fprintf(csv->file, "时间戳,O2(%%),CO(ppm),传感器温度(℃),压力表(kPa),抽气泵电流(A),炉膛实际温度(℃),实际炉压(Pa),给煤量(t/h),实际生料量1(t/h),实际生料量2(t/h),引风机转速(rpm),给煤量设定(t/h),生料量1设定(t/h),生料量2设定(t/h),引风机转速设定(rpm)\n");
        fflush(csv->file);  // 立即刷新表头到磁盘
        debug_printf("写入CSV表头到新文件: %s\n", csv->filename);
    } else {
        debug_printf("文件已存在，追加数据到: %s\n", csv->filename);
    }

    csv->current_size = ftell(csv->file);
    csv->buffer_size = 0;

    // 设置日期检查时间变量，为新文件的日期检查做准备
    // 修复bug：不要重置为0，而是设置为当前时间，这样下次检查时能正确比较日期变化
    time_t current_time = time(NULL);
    csv->last_check_time = current_time;
    csv->current_check_time = current_time;

    // 清除创建状态标志
    csv->creating_file = 0;

    debug_printf("创建新CSV文件成功: %s (日期: %d)\n", csv->filename, csv->current_date);
    return 0;
}

// 检查是否需要创建新的日期文件
int need_new_date_file(csv_manager_t *csv) {
    // 如果文件还没有创建，不需要检查日期变化
    if (!csv->file) {
        return 0;
    }

    // 如果正在创建文件，不要重复检查
    if (csv->creating_file) {
        return 0;
    }

    // 获取当前时间
    time_t now = time(NULL);
    struct tm *current_tm = localtime(&now);

    // 计算当前日期（YYYYMMDD格式）
    int current_date = (current_tm->tm_year + 1900) * 10000 +
                      (current_tm->tm_mon + 1) * 100 +
                      current_tm->tm_mday;

    // 如果当前日期和文件日期不同，需要创建新文件
    if (csv->current_date != current_date) {
        debug_printf("检测到日期变化，需要创建新的CSV文件\n");
        debug_printf("文件日期: %d, 当前日期: %d\n", csv->current_date, current_date);
        return 1;  // 需要新文件
    }

    return 0;  // 不需要新文件
}

// 将数据写入CSV缓冲区
int write_to_csv_buffer(csv_manager_t *csv, const unsigned char *data, size_t length) {
    // 检查是否需要创建新文件（日期变化、文件大小超限或首次创建）
    int need_new_file = 0;

    if (!csv->file) {
        need_new_file = 1;
        debug_printf("文件未打开，需要创建新文件\n");
    } else if (need_new_date_file(csv)) {
        need_new_file = 1;
        debug_printf("日期变化，需要创建新文件\n");
    } else if (csv->max_size > 0 && csv->current_size >= csv->max_size) {
        need_new_file = 1;
        debug_printf("文件大小超限，需要创建新文件\n");
    }

    if (need_new_file) {
        debug_printf("开始创建新的CSV文件...\n");
        if(create_new_csv_file(csv) != 0) {
            debug_printf("创建新CSV文件失败\n");
            return -1;
        }
        debug_printf("新CSV文件创建完成\n");
    }

    // 获取当前时间戳
    time_t now = time(NULL);

    // 格式化数据行
    char line[2048];
    int line_length = snprintf(line, sizeof(line), "%ld,%s\n", now,data);

    // 检查缓冲区是否有足够空间
    if(csv->buffer_size + line_length >= sizeof(csv->buffer)) {
        debug_printf("buffer sufficent refresh!\n");
        // 缓冲区已满，刷新到文件
        if(flush_csv_buffer(csv) != 0) {
            return -1;
        }
    }

    // 将时间戳和数据长度添加到缓冲区
    memcpy(csv->buffer + csv->buffer_size, line, line_length);
    csv->buffer_size += line_length;

    // 检查是否需要刷新缓冲区
    time_t current_time = time(NULL);
    if(csv->buffer_size >= csv->flush_threshold ||
       current_time - csv->last_flush_time >= csv->flush_interval) {
        debug_printf("timeover refresh!\n");
        if(flush_csv_buffer(csv) != 0) {
            return -1;
        }
    }

    return 0;
}

// 刷新CSV缓冲区到文件
int flush_csv_buffer(csv_manager_t *csv) {
    if(!csv->file || csv->buffer_size == 0) {
        return 0;
    }

    size_t written = fwrite(csv->buffer, 1, csv->buffer_size, csv->file);
    if(written != csv->buffer_size) {
        debug_perror("写入CSV文件失败");
        return -1;
    }

    // 强制刷新文件缓冲区，确保数据立即写入磁盘
    fflush(csv->file);

    csv->current_size += csv->buffer_size;
    csv->buffer_size = 0;
    csv->last_flush_time = time(NULL);

    return 0;
}

// 关闭CSV文件
void close_csv_manager(csv_manager_t *csv) {
    if(csv->file) {
        // 确保所有数据都写入文件
        if(csv->buffer_size > 0) {
            flush_csv_buffer(csv);
        }

        fclose(csv->file);
        csv->file = NULL;
        debug_printf("CSV文件已关闭: %s\n", csv->filename);
    }
}


